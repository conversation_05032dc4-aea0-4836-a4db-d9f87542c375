/**
 * Asset path utility for handling paths in both development and production (GitHub Pages)
 *
 * This resolves issues with file paths in GitHub Pages deployment where
 * the base URL includes the repository name: /3D-Portfolio/
 */

/**
 * Get the correct asset path based on environment
 * @param {string} path - The relative path to the asset (should start with /)
 * @returns {string} The correct asset path for the current environment
 */
export function getAssetPath(path) {
  // Get the base URL from the current environment
  const baseUrl = import.meta.env?.BASE_URL || '/3D-Portfolio/';

  // Remove any leading double slashes and ensure path starts with /
  let cleanPath = path.replace(/^\/+/, '/');

  // Handle shader paths
  if (cleanPath.includes('/src/shaders/')) {
    cleanPath = cleanPath.replace('/src/shaders/', '/shaders/');
  }
  if (cleanPath.includes('./src/shaders/')) {
    cleanPath = cleanPath.replace('./src/shaders/', '/shaders/');
  }

  // Handle root GLB files - ensure they're accessible from root
  if (cleanPath.match(/^\/[^\/]+\.glb$/)) {
    // This is a root GLB file like /chair.glb
    cleanPath = cleanPath.replace(/^\//, '');
  }

  // Handle assets paths - ensure consistency
  if (cleanPath.startsWith('/assets/')) {
    cleanPath = cleanPath.replace(/^\//, '');
  }

  // Handle models, fonts, sounds, images paths
  if (cleanPath.startsWith('/models/') ||
      cleanPath.startsWith('/fonts/') ||
      cleanPath.startsWith('/sounds/') ||
      cleanPath.startsWith('/images/')) {
    cleanPath = cleanPath.replace(/^\//, '');
  }

  // Remove leading slash to avoid double slashes when joining with baseUrl
  cleanPath = cleanPath.replace(/^\//, '');

  // Join with base URL
  const finalPath = baseUrl + cleanPath;

  // Log for debugging in development
  if (import.meta.env.DEV) {
    console.log(`[AssetPath] ${path} -> ${finalPath}`);
  }

  return finalPath;
}