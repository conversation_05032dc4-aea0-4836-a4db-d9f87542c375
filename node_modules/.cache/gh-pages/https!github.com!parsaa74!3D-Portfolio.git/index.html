<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive 3D Experience</title>
  <link rel="icon" href="/3D-Portfolio/assets/corridor-icon.svg" type="image/svg+xml">
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      width: 100vw;
      height: 100vh;
      background: black;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      color: #e0e0e0;
    }
    #app-container {
      position: relative;
      width: 100%;
      height: 100%;
    }
    #three-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10;
    }
    .hud {
      position: fixed;
      z-index: 10;
    }

    /* Remove old start button styles */
    #three-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    /* Game HUD Styles */
    #game-hud {
      display: none; /* Hide initially */
      padding: 20px;
    }

    #location-indicator {
      font-size: 1.2em;
      margin-bottom: 15px;
      color: #5CDED3;
    }

    #controls-info {
      position: fixed;
      bottom: 25px;
      left: 25px;
      width: 180px;
      font-family: 'Neue Montreal', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      font-size: 13px;
      color: #5CDED3;
      background-color: rgba(16, 24, 32, 0.85);
      border-left: 2px solid #5CDED3;
      backdrop-filter: blur(5px);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      z-index: 100;
      letter-spacing: 1.2px;
      transition: transform 0.3s ease, opacity 0.3s ease;
      transform-origin: bottom left;
    }
    
    #controls-info:hover {
      transform: scale(1.03);
    }
    
    #controls-header {
      background-color: rgba(92, 222, 211, 0.15);
      padding: 8px 12px;
      font-weight: 500;
      font-size: 11px;
      text-transform: uppercase;
      letter-spacing: 2px;
      border-bottom: 1px solid rgba(92, 222, 211, 0.3);
    }
    
    .control-items {
      padding: 10px 12px;
    }
    
    .control-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      line-height: 1.5;
    }
    
    .control-item:last-child {
      margin-bottom: 0;
    }
    
    .key {
      color: white;
      font-weight: 500;
    }

    #interaction-prompt {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.7);
      padding: 15px;
      border-radius: 5px;
      display: none;
      z-index: 9999;
      border: 2px solid #5CDED3;
      box-shadow: 0 0 15px rgba(92, 222, 211, 0.5);
      font-size: 18px;
      font-weight: bold;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0% { transform: translate(-50%, -50%) scale(1); opacity: 0.9; }
      50% { transform: translate(-50%, -50%) scale(1.05); opacity: 1; }
      100% { transform: translate(-50%, -50%) scale(1); opacity: 0.9; }
    }

    #subtitle-container {
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      font-size: 1.2em;
      z-index: 1000;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    #dev-terminal-overlay {
      background: #000 !important;
      color: #33ff33 !important;
      border: 2px solid #33ff33 !important;
      border-radius: 4px !important;
      box-shadow: 0 0 10px rgba(51, 255, 51, 0.5) !important;
      font-family: 'Fira Mono', 'Courier New', monospace !important;
      padding: 0 !important;
    }

    #dev-terminal-body {
      font-family: 'Fira Mono', 'Courier New', monospace;
      color: #33ff33;
      background-color: #000;
      margin: 25px 15px 0 15px;
      padding: 5px;
      font-size: 14px;
      line-height: 1.4;
      overflow-y: auto;
      height: 320px;
      max-height: 320px;
      border: none;
    }

    #dev-terminal-prompt {
      font-family: 'Fira Mono', 'Courier New', monospace;
      color: #33ff33;
      margin: 5px 15px 15px 15px;
      font-size: 14px;
      display: flex;
      align-items: center;
      height: 25px;
    }

    .dev-terminal-cursor {
      display: inline-block;
      width: 8px;
      height: 14px;
      background: #33ff33;
      margin-left: 2px;
      animation: blink-cursor 1s steps(1) infinite;
      vertical-align: middle;
    }

    #dev-terminal-header {
      height: 25px;
      background: #202020;
      border-bottom: 1px solid #33ff33;
      display: flex;
      align-items: center;
      padding: 0 10px;
      font-size: 12px;
      color: #33ff33;
      font-weight: bold;
      justify-content: space-between;
    }

    #dev-terminal-title {
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    #dev-terminal-controls {
      display: flex;
    }

    .terminal-btn {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-left: 8px;
      cursor: pointer;
    }

    .terminal-close {
      background-color: #ff3b30;
    }

    .terminal-minimize {
      background-color: #ffcc00;
    }

    .terminal-maximize {
      background-color: #28cd41;
    }

    @keyframes blink-cursor {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }

    .terminal-scan-line {
      background: linear-gradient(
        to bottom,
        rgba(51, 255, 51, 0.03) 0%,
        rgba(51, 255, 51, 0.02) 50%,
        rgba(51, 255, 51, 0.01) 100%
      );
      position: absolute;
      top: 25px;
      left: 0;
      width: 100%;
      height: calc(100% - 25px);
      pointer-events: none;
      z-index: 10;
    }

    .terminal-command {
      color: #33ff33;
    }

    .terminal-output {
      color: #cccccc;
      margin-bottom: 8px;
    }

    .terminal-error {
      color: #ff3333;
    }
  </style>

  <!-- Performance monitoring and error handling -->
  <script>
    // Performance monitoring
    window.performance.mark('page-start');
    
    // Global error handler to prevent unhandled exceptions from crashing the application
    window.addEventListener('error', function(event) {
      console.error('Global error caught:', {
        error: event.error,
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
      
      // Show error to user for critical errors
      const errorMessage = event.error ? (event.error.stack || event.error.message) : event.message;
      if (errorMessage.includes('WebGL') || errorMessage.includes('THREE') || errorMessage.includes('memory')) {
        showErrorOverlay(errorMessage);
      }
      
      event.preventDefault();
      return true;
    });
    
    // Also catch unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      console.error('Unhandled promise rejection:', {
        reason: event.reason,
        stack: event.reason?.stack
      });
      event.preventDefault();
    });
    
    // Show error overlay
    function showErrorOverlay(message) {
      const errorOverlay = document.createElement('div');
      errorOverlay.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        right: 10px;
        background: rgba(255, 0, 0, 0.8);
        color: white;
        padding: 15px;
        border-radius: 5px;
        z-index: 100000;
        font-family: sans-serif;
        max-height: 200px;
        overflow-y: auto;
      `;
      errorOverlay.innerHTML = `
        <h3>An error occurred</h3>
        <p>${message}</p>
        <button onclick="window.location.reload()" style="background: white; border: none; padding: 5px 10px; margin-top: 10px; cursor: pointer;">
          Reload
        </button>
        <button onclick="this.parentNode.remove()" style="background: #333; color: white; border: none; padding: 5px 10px; margin-top: 10px; margin-left: 10px; cursor: pointer;">
          Dismiss
        </button>
      `;
      document.body.appendChild(errorOverlay);
    }
    
    // Simplified debug logger
    window.debugLumon = {
      log: function(message) {
        console.log("%c[Lumon Debug] " + message, "color: #5CDED3");
      },
      error: function(message) {
        console.error("%c[Lumon Debug] " + message, "color: #FF5555");
      }
    };
  </script>
  
  
  
  
  <script type="module" crossorigin src="/3D-Portfolio/assets/index-abe4efcc.js"></script>
  <link rel="stylesheet" href="/3D-Portfolio/assets/index.css">
</head>
<body>
  <div id="app-container">
    <div id="three-container"></div>

    <div id="subtitle-container"></div>
    <div id="ui-overlay">
      <div id="location-indicator"></div>
      <div id="interaction-prompt" style="display: none;"></div>
      <!-- Dev Terminal Overlay (hidden by default) -->
      <div id="dev-terminal-overlay" style="display:none; position:fixed; left:5vw; top:15vh; width:700px; height:400px; background:#000; color:#33ff33; border:2px solid #33ff33; border-radius:4px; z-index:10000; font-family:'Fira Mono', 'Courier New', monospace; box-shadow:0 0 10px rgba(51, 255, 51, 0.5); padding:0; overflow:hidden;">
        <div id="dev-terminal-header">
          <div id="dev-terminal-title">TERMINAL</div>
          <div id="dev-terminal-controls">
            <div class="terminal-btn terminal-minimize"></div>
            <div class="terminal-btn terminal-maximize"></div>
            <div class="terminal-btn terminal-close" id="dev-terminal-close"></div>
          </div>
        </div>
        <div class="terminal-scan-line"></div>
        <div id="dev-terminal-body"></div>
        <div id="dev-terminal-prompt"></div>
      </div>
    </div>

    <!-- Game HUD -->
    <div id="game-hud" class="hud">
      <!-- Improved controls info box -->
      <div id="controls-info">
        <div id="controls-header">CONTROLS</div>
        <div class="control-items">
          <div class="control-item">
            <span class="key">WASD</span>
            <span>MOVE</span>
          </div>
          <div class="control-item">
            <span class="key">ARROWS</span>
            <span>LOOK</span>
          </div>
          <div class="control-item">
            <span class="key">MOUSE</span>
            <span>LOOK</span>
          </div>
          <div class="control-item">
            <span class="key">SHIFT</span>
            <span>RUN</span>
          </div>
          <div class="control-item">
            <span class="key">E</span>
            <span>INTERACT</span>
          </div>
          <div class="control-item">
            <span class="key">SCROLL</span>
            <span>ZOOM</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script>
    // Initialize UI components after DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      // Make sure controls are visible by default
      const controlsInfo = document.getElementById('controls-info');
      if (controlsInfo && controlsInfo.classList.contains('hidden')) {
        controlsInfo.classList.remove('hidden');
      }
      
      // Test terminal function (for development)
      window.openTerminal = function() {
        if (typeof window.showDevTerminalOverlay === 'function') {
          window.showDevTerminalOverlay();
        }
      };
      
      // Keyboard shortcut for testing terminal (Alt + T)
      document.addEventListener('keydown', function(e) {
        if (e.altKey && e.key === 't') {
          window.openTerminal();
        }
      });
    });

    // Add global mouse tracking for shaders
    window.mouseX = window.innerWidth / 2;
    window.mouseY = window.innerHeight / 2;
    
    document.addEventListener('mousemove', (event) => {
      window.mouseX = event.clientX;
      window.mouseY = event.clientY;
    });
  </script>
</body>
</html>