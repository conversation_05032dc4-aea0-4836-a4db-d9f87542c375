// vite.config.js
import { defineConfig } from 'vite'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import fs from 'fs'
import path from 'path'

// Use dynamic import for ESM-only package
const glsl = (await import('vite-plugin-glsl')).default;

export default defineConfig({
  base: '/3D-Portfolio/', // Changed from './' to '/3D-Portfolio/' for GitHub Pages
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      output: {
        assetFileNames: 'assets/[name].[ext]'
      }
    }
  },
  publicDir: 'public',
  plugins: [
    glsl(),
    viteStaticCopy({
      targets: [
        // Copy all assets to the assets directory
        {
          src: 'assets/**/*',
          dest: 'assets'
        },
        // Copy shaders to both locations for compatibility
        {
          src: 'src/shaders/**/*',
          dest: 'shaders'
        },
        {
          src: 'shaders/**/*',
          dest: 'shaders'
        },
        // Copy models to both root and assets/models for compatibility
        {
          src: 'models/**/*',
          dest: 'models'
        },
        {
          src: 'models/**/*',
          dest: 'assets/models'
        },
        // Copy fonts to both locations for compatibility
        {
          src: 'fonts/**/*',
          dest: 'fonts'
        },
        {
          src: 'fonts/**/*',
          dest: 'assets/fonts'
        },
        // Copy sounds to both locations for compatibility
        {
          src: 'sounds/**/*',
          dest: 'sounds'
        },
        {
          src: 'sounds/**/*',
          dest: 'assets/sounds'
        },
        // Copy textures to assets/textures
        {
          src: 'textures/**/*',
          dest: 'assets/textures'
        },
        // Copy images to assets/images
        {
          src: 'images/**/*',
          dest: 'assets/images'
        },
        // Copy root GLB files to both root and assets for compatibility
        {
          src: '*.glb',
          dest: '.'
        },
        {
          src: '*.glb',
          dest: 'assets'
        }
      ]
    }),
    {
      name: 'create-nojekyll',
      closeBundle() {
        // Create .nojekyll file to prevent GitHub Pages from using Jekyll
        const outDir = path.resolve('dist');
        fs.writeFileSync(path.join(outDir, '.nojekyll'), '');
        console.log('Created .nojekyll file');
      }
    }
  ]
})
